# Synctix 票務平台 - 深度專案分析報告

## 1. 專案概覽與核心目標

**Synctix** 是一套整合性的演唱會票務平台，專為 2025 第四屆金匠獎題目二而開發。該平台能同時處理數場活動的票務銷售，並可因應高流量、高併發的搶票行為。系統採用現代化的前後端分離架構，提供完整的票券購買、管理和後台管理功能，具備響應式設計和深色主題風格，旨在解決傳統票務系統在高併發情境下的技術挑戰。

## 2. 功能清單 / 網站地圖

### **主要功能分類一：用戶身份驗證與管理**
- **用戶註冊** - *支援 Google OAuth 登入，包含姓名、電話等個人資訊*
- **用戶登入/登出** - *Google OAuth 登入*
- **個人資料管理** - *查看和編輯個人資訊、購票記錄*
- **訂單查詢與票券管理** - *查看購票歷史、票券狀態、QR Code*

### **主要功能分類二：活動展示與搜尋**
- **活動瀏覽** - *分頁顯示演唱會列表，支援響應式設計*
- **搜尋與篩選** - *按歌手、地點、分類進行搜尋和篩選*
- **活動詳情** - *完整活動資訊、票種選擇、重要注意事項*
- **主頁輪播海報** - *精選活動的視覺化展示*

### **主要功能分類三：購票流程**
- **選擇場次與票種** - *多票種選擇、庫存即時顯示*
- **購票數量選擇** - *限制 1-10 張，防止大量囤票*
- **個人資料填寫** - *會員自動填入，訪客手動填寫*
- **付款處理** - *模擬多種付款方式（信用卡、ATM、超商）*
- **購票成功確認** - *生成 QR Code 票券*

### **主要功能分類四：後台管理系統**
- **管理員儀表板** - *統計數據、最近訂單、熱門活動分析*
- **活動管理** - *新增、編輯、刪除活動；管理票種與庫存*
- **訂單管理** - *查看所有訂單、訂單詳情、銷售統計*
- **用戶管理** - *查看註冊用戶列表、用戶資訊*

### **主要功能分類五：系統特色功能**
- **高併發處理** - *庫存控制、防止超賣機制*
- **風控機制** - *限購機制、防黃牛設計*
- **視覺效果** - *現代科技風格動態背景、粒子系統、毛玻璃效果*
- **響應式設計** - *適配桌面、平板、手機各種裝置*

## 3. 使用技術與框架

### **前端 (Frontend):**
- **語言**: JavaScript (ES6+)
- **框架**: React 19.1.0 + Vite 7.0.4 - *選擇原因：現代化開發體驗、快速熱重載、優秀的構建性能*
- **UI 框架**: Material-UI (MUI) 7.2.0 - *提供一致的 Material Design 風格組件*
- **樣式**: TailwindCSS 4.1.11 + Emotion - *實用優先的 CSS 框架，配合 MUI 的情感化樣式*
- **路由**: React Router DOM 7.6.3 - *單頁應用路由管理*
- **狀態管理**: React Context API - *輕量級全域狀態管理，適合中小型專案*
- **HTTP 客戶端**: Axios 1.10.0 - *處理 API 請求和響應*

### **後端 (Backend):**
- **語言**: Python 3.11+
- **框架**: FastAPI 0.116.1 - *選擇原因：高性能、自動 API 文檔生成、現代 Python 異步支援*
- **認證**: 密碼雜湊 (SHA-256) + Google OAuth 2.0 - *多重認證方式*
- **API 文檔**: 自動生成 Swagger UI - *FastAPI 內建功能*
- **伺服器**: Uvicorn 0.35.0 - *高性能 ASGI 伺服器*

### **資料庫 (Database):**
- SQLAlchemy 2.0.41 - *選擇原因：強大的 ORM 功能、易於擴展至 MySQL、PostgreSQL 等關聯式資料庫*

### **外部 API:**
- **Google Identity Services** - *用於 Google OAuth 登入功能*
- **QR Code API** - *生成票券 QR Code (qrserver.com)*

### **開發與部署 (DevOps):**
- **Package Management**: npm (前端) + uv (Python 後端)
- **程式碼品質**: ESLint + Ruff (Python linter)
- **類型檢查**: MyPy (Python)
- **開發工具**: Vite 開發伺服器、FastAPI 自動重載

## 4. 使用者案例分析

### **角色 1: 一般訪客**
- 瀏覽演唱會列表
- 搜尋特定演出
- 查看活動詳情
- 以訪客身份購票
- 註冊成為會員

### **角色 2: 註冊會員**
- 登入/登出系統
- 快速購票（自動填入個人資料）
- 查看購票記錄
- 管理個人資料
- 使用 Google 帳號登入

### **角色 3: 系統管理員**
- 管理員登入後台
- 查看營運儀表板
- 新增/編輯/刪除演唱會
- 管理票種和庫存
- 查看所有訂單和用戶
- 監控銷售統計

## 5. 資料分析模型 / 核心演算法說明

本專案主要包含以下資料處理和演算法邏輯：

### **庫存管理演算法**
- **輸入資料**: 票種庫存數量、用戶購買請求
- **處理步驟**: 
  1. 檢查票種是否存在
  2. 驗證庫存是否足夠
  3. 原子性更新庫存數量
  4. 創建訂單記錄
- **輸出結果**: 更新後的庫存狀態、訂單確認

### **搜尋與篩選演算法**
- **輸入資料**: 用戶搜尋關鍵字、篩選條件
- **處理步驟**: 
  1. 多欄位模糊搜尋（演出名稱、歌手、地點）
  2. 分類標籤篩選
  3. 狀態篩選（即將開售、熱賣中等）
  4. 分頁處理
- **輸出結果**: 符合條件的演唱會列表

### **用戶認證與授權**
- **輸入資料**: 用戶憑證（密碼或 Google Token）
- **處理步驟**: 
  1. 密碼雜湊驗證或 Google Token 驗證
  2. 用戶角色檢查（一般用戶/管理員）
  3. 會話狀態管理
- **輸出結果**: 認證結果、用戶權限資訊

## 6. 成果展示

### **功能 A 展示 (用戶註冊與登入流程):**
- **說明**: AuthContext 負責前端用戶狀態管理，main.py 中的認證 API 處理後端驗證邏輯

### **功能 B 展示 (購票流程):**
- **說明**: 完整的購票流程，從選擇票種到付款確認，包含表單驗證和庫存檢查

### **功能 C 展示 (管理員後台):**
- **說明**: 管理員儀表板展示營運數據，活動管理對話框處理 CRUD 操作

### **功能 D 展示 (視覺效果系統):**
- **說明**: 現代科技風格的動態背景和粒子系統，提供沉浸式視覺體驗

## 7. 開發挑戰與解決方案

### **挑戰 1: 高併發票務系統的庫存控制問題**
- **解決方案**: 使用 Go 語言和 RabbitMQ 實現高併發下的庫存控制和訂單處理

### **挑戰 2: 前後端分離架構的狀態同步**
- **解決方案**: 採用 React Context API 進行全域狀態管理，實現了用戶狀態的持久化和同步

### **挑戰 3: 複雜的 UI 動畫效果性能優化**
- **解決方案**: 使用 Canvas API 和 requestAnimationFrame 實現高效能的粒子動畫系統

### **挑戰 4: 多種認證方式的整合**
- **解決方案**: 使用 Google OAuth 的統一處理，前端 `GoogleLoginButton.jsx` 組件處理 Google 登入的複雜邏輯

### **挑戰 5: 響應式設計的一致性**
- **推斷的解決方案**: 結合 Material-UI 的響應式組件和 TailwindCSS 的實用類別，確保在各種裝置上的一致體驗

## 8. 功能開發分工
### **前端開發分工推測:**
- **UI/UX 設計師**: 負責 `ArknightsBackground.jsx`、`ParticleSystem.jsx` 等視覺效果組件
- **前端工程師 A**: 負責用戶認證相關頁面 (`LoginPage.jsx`, `RegisterPage.jsx`, `AuthContext.jsx`)
- **前端工程師 B**: 負責購票流程頁面 (`PurchasePage.jsx`, `CheckoutPage.jsx`, `PaymentPage.jsx`)
- **前端工程師 C**: 負責管理員後台 (`AdminDashboard.jsx`, `ConcertManagementDialog.jsx`)

### **後端開發分工推測:**
- **後端工程師 A**: 負責核心 API 架構 (`main.py`, `schemas.py`)
- **後端工程師 B**: 負責資料庫系統 (`json_database.py`, `data_initializer.py`)
- **後端工程師 C**: 負責認證與安全 (`security.py`, Google OAuth 整合)

### **全端協作功能:**
- **API 整合**: 前後端工程師共同協作完成 API 介面設計
- **資料模型設計**: 團隊共同設計資料結構和 API 規格
- **測試與部署**: 整個團隊協作完成系統測試和部署配置

---

這份分析報告涵蓋了 Synctix 票務平台的所有核心面向，可以作為您簡報的完整技術基礎。每個部分都提供了足夠的細節來支撐您的演示和技術說明。
